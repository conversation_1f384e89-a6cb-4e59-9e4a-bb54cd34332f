/**
 * واجهة الهاتف المحمول المحسنة - Loacker
 * إصدار 2025 - JavaScript محسن ومتوافق مع جميع الأجهزة
 */

class MobileInterface {
    constructor() {
        this.isInitialized = false;
        this.currentTab = 'map';
        this.map = null;
        this.markers = [];
        this.stores = [];
        this.selectedLocation = null;
        this.isLoading = false;
        
        // التحقق من أن الجهاز محمول
        if (!this.isMobileDevice()) {
            console.log('📱 ليس جهاز محمول - تخطي تهيئة واجهة الهاتف');
            return;
        }
        
        this.init();
    }
    
    /**
     * التحقق من أن الجهاز محمول
     */
    isMobileDevice() {
        return document.body.classList.contains('mobile-device') || 
               window.innerWidth <= 768 ||
               /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    }
    
    /**
     * تهيئة الواجهة
     */
    async init() {
        try {
            console.log('📱 بدء تهيئة واجهة الهاتف المحمول...');
            
            // إنشاء هيكل الواجهة
            this.createMobileInterface();
            
            // تهيئة الأحداث
            this.setupEventListeners();
            
            // تهيئة الخريطة
            await this.initializeMap();
            
            // تحميل البيانات
            await this.loadInitialData();
            
            this.isInitialized = true;
            console.log('✅ تم تهيئة واجهة الهاتف المحمول بنجاح');
            
        } catch (error) {
            console.error('❌ خطأ في تهيئة واجهة الهاتف المحمول:', error);
            this.showError('حدث خطأ في تحميل الواجهة');
        }
    }
    
    /**
     * إنشاء هيكل واجهة الهاتف المحمول
     */
    createMobileInterface() {
        // التحقق من وجود الواجهة
        let mobileView = document.querySelector('.mobile-view');
        if (!mobileView) {
            mobileView = document.createElement('div');
            mobileView.className = 'mobile-view';
            document.body.appendChild(mobileView);
        }
        
        // إنشاء الهيكل الأساسي
        mobileView.innerHTML = `
            <!-- الرأس -->
            <header class="mobile-header">
                <div class="mobile-logo">
                    <div class="mobile-logo-icon">
                        <i class="fas fa-store"></i>
                    </div>
                    <div class="mobile-logo-text">Loacker</div>
                </div>
                <button class="mobile-menu-button" id="mobileMenuButton">
                    <i class="fas fa-bars"></i>
                </button>
            </header>
            
            <!-- المحتوى الرئيسي -->
            <main class="mobile-content">
                <!-- محتوى الخريطة -->
                <div class="mobile-tab-content active" id="mobile-map-content">
                    <div class="mobile-card">
                        <div class="mobile-card-body" style="padding: 0;">
                            <div id="mobile-map" style="height: 50vh; border-radius: 16px; overflow: hidden;"></div>
                        </div>
                    </div>
                    
                    <!-- أزرار الخريطة -->
                    <div style="display: flex; gap: 12px; margin-top: 16px;">
                        <button class="mobile-button mobile-button-outline" id="getCurrentLocationBtn">
                            <i class="fas fa-location-arrow"></i>
                            موقعي
                        </button>
                        <button class="mobile-button mobile-button-secondary" id="searchLocationBtn">
                            <i class="fas fa-search"></i>
                            بحث
                        </button>
                    </div>
                </div>
                
                <!-- محتوى قائمة المتاجر -->
                <div class="mobile-tab-content" id="mobile-stores-content">
                    <!-- شريط البحث -->
                    <div class="mobile-form-group">
                        <input type="text" class="mobile-input" id="storeSearchInput" placeholder="البحث عن متجر...">
                    </div>
                    
                    <!-- قائمة المتاجر -->
                    <div id="mobile-stores-list">
                        <!-- سيتم ملؤها بواسطة JavaScript -->
                    </div>
                </div>
                
                <!-- محتوى إضافة متجر -->
                <div class="mobile-tab-content" id="mobile-add-content">
                    <form id="mobile-store-form">
                        <div class="mobile-form-group">
                            <label class="mobile-label">اسم المتجر *</label>
                            <input type="text" class="mobile-input" id="storeName" required>
                        </div>
                        
                        <div class="mobile-form-group">
                            <label class="mobile-label">رقم الهاتف</label>
                            <input type="tel" class="mobile-input" id="storePhone">
                        </div>
                        
                        <div class="mobile-form-group">
                            <label class="mobile-label">العنوان *</label>
                            <input type="text" class="mobile-input" id="storeAddress" required>
                        </div>
                        
                        <div class="mobile-form-group">
                            <label class="mobile-label">صورة المتجر</label>
                            <input type="file" class="mobile-input" id="storeImage" accept="image/*">
                        </div>
                        
                        <div class="mobile-form-group">
                            <label class="mobile-label">الموقع على الخريطة</label>
                            <div id="selectedLocationText" style="padding: 12px; background: #f3f4f6; border-radius: 8px; font-size: 14px; color: #6b7280;">
                                انقر على الخريطة لتحديد الموقع
                            </div>
                        </div>
                        
                        <button type="submit" class="mobile-button mobile-button-primary" style="width: 100%;">
                            <i class="fas fa-plus"></i>
                            إضافة المتجر
                        </button>
                    </form>
                </div>
            </main>
            
            <!-- التبويبات السفلية -->
            <nav class="mobile-tabs">
                <button class="mobile-tab-button active" data-tab="map">
                    <i class="fas fa-map mobile-tab-icon"></i>
                    <span>الخريطة</span>
                </button>
                <button class="mobile-tab-button" data-tab="stores">
                    <i class="fas fa-list mobile-tab-icon"></i>
                    <span>المتاجر</span>
                </button>
                <button class="mobile-tab-button" data-tab="add">
                    <i class="fas fa-plus mobile-tab-icon"></i>
                    <span>إضافة</span>
                </button>
            </nav>
            
            <!-- منطقة الرسائل -->
            <div id="mobile-alerts" style="position: fixed; top: 80px; left: 20px; right: 20px; z-index: 1001;"></div>
        `;
    }
    
    /**
     * إعداد مستمعي الأحداث
     */
    setupEventListeners() {
        // أحداث التبويبات
        document.querySelectorAll('.mobile-tab-button').forEach(button => {
            button.addEventListener('click', (e) => {
                const tab = e.currentTarget.dataset.tab;
                this.switchTab(tab);
            });
        });
        
        // زر الموقع الحالي
        const getCurrentLocationBtn = document.getElementById('getCurrentLocationBtn');
        if (getCurrentLocationBtn) {
            getCurrentLocationBtn.addEventListener('click', () => {
                this.getCurrentLocation();
            });
        }
        
        // نموذج إضافة متجر
        const storeForm = document.getElementById('mobile-store-form');
        if (storeForm) {
            storeForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.submitStore();
            });
        }
        
        // البحث في المتاجر
        const searchInput = document.getElementById('storeSearchInput');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.searchStores(e.target.value);
            });
        }
    }
    
    /**
     * تبديل التبويبات
     */
    switchTab(tabName) {
        // إزالة الفئة النشطة من جميع الأزرار والمحتويات
        document.querySelectorAll('.mobile-tab-button').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelectorAll('.mobile-tab-content').forEach(content => {
            content.classList.remove('active');
        });
        
        // إضافة الفئة النشطة للتبويب المحدد
        const activeButton = document.querySelector(`[data-tab="${tabName}"]`);
        const activeContent = document.getElementById(`mobile-${tabName}-content`);
        
        if (activeButton) activeButton.classList.add('active');
        if (activeContent) activeContent.classList.add('active');
        
        this.currentTab = tabName;
        
        // تحديث الخريطة إذا كان التبويب النشط هو الخريطة
        if (tabName === 'map' && this.map) {
            setTimeout(() => {
                this.map.invalidateSize();
            }, 100);
        }
    }
    
    /**
     * تهيئة الخريطة
     */
    async initializeMap() {
        try {
            // التحقق من وجود مكتبة Leaflet
            if (typeof L === 'undefined') {
                throw new Error('مكتبة Leaflet غير متوفرة');
            }
            
            // إنشاء الخريطة
            this.map = L.map('mobile-map', {
                center: [32.8872, 13.1913], // طرابلس، ليبيا
                zoom: 13,
                zoomControl: true,
                attributionControl: false
            });
            
            // إضافة طبقة الخريطة
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors'
            }).addTo(this.map);
            
            // إضافة مستمع النقر على الخريطة
            this.map.on('click', (e) => {
                this.setSelectedLocation(e.latlng);
            });
            
            console.log('✅ تم تهيئة الخريطة بنجاح');
            
        } catch (error) {
            console.error('❌ خطأ في تهيئة الخريطة:', error);
            this.showError('حدث خطأ في تحميل الخريطة');
        }
    }
    
    /**
     * تحديد الموقع المختار
     */
    setSelectedLocation(latlng) {
        this.selectedLocation = latlng;
        
        // إزالة العلامة السابقة
        if (this.selectedMarker) {
            this.map.removeLayer(this.selectedMarker);
        }
        
        // إضافة علامة جديدة
        this.selectedMarker = L.marker(latlng, {
            icon: L.divIcon({
                html: '<i class="fas fa-map-marker-alt" style="color: #d50000; font-size: 24px;"></i>',
                iconSize: [24, 24],
                iconAnchor: [12, 24],
                className: 'selected-location-marker'
            })
        }).addTo(this.map);
        
        // تحديث النص
        const locationText = document.getElementById('selectedLocationText');
        if (locationText) {
            locationText.innerHTML = `
                <i class="fas fa-map-marker-alt" style="color: #d50000;"></i>
                ${latlng.lat.toFixed(6)}, ${latlng.lng.toFixed(6)}
            `;
            locationText.style.background = '#d1fae5';
            locationText.style.color = '#065f46';
        }
        
        // التبديل إلى تبويب الإضافة
        this.switchTab('add');
    }
    
    /**
     * الحصول على الموقع الحالي
     */
    getCurrentLocation() {
        if (!navigator.geolocation) {
            this.showError('متصفحك لا يدعم تحديد الموقع');
            return;
        }
        
        const button = document.getElementById('getCurrentLocationBtn');
        const originalText = button.innerHTML;
        
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحديد...';
        button.disabled = true;
        
        navigator.geolocation.getCurrentPosition(
            (position) => {
                const { latitude, longitude } = position.coords;
                
                // الانتقال إلى الموقع
                this.map.setView([latitude, longitude], 16);
                
                // إضافة علامة للموقع الحالي
                const currentLocationMarker = L.marker([latitude, longitude], {
                    icon: L.divIcon({
                        html: '<i class="fas fa-crosshairs" style="color: #3b82f6; font-size: 20px; animation: pulse 2s infinite;"></i>',
                        iconSize: [20, 20],
                        iconAnchor: [10, 10],
                        className: 'current-location-marker'
                    })
                }).addTo(this.map);
                
                // إزالة العلامة بعد 5 ثواني
                setTimeout(() => {
                    this.map.removeLayer(currentLocationMarker);
                }, 5000);
                
                button.innerHTML = originalText;
                button.disabled = false;
                
                this.showSuccess('تم تحديد موقعك الحالي');
            },
            (error) => {
                let message = 'حدث خطأ في تحديد الموقع';
                
                switch (error.code) {
                    case error.PERMISSION_DENIED:
                        message = 'تم رفض الوصول إلى الموقع';
                        break;
                    case error.POSITION_UNAVAILABLE:
                        message = 'معلومات الموقع غير متوفرة';
                        break;
                    case error.TIMEOUT:
                        message = 'انتهت مهلة طلب الموقع';
                        break;
                }
                
                this.showError(message);
                button.innerHTML = originalText;
                button.disabled = false;
            },
            {
                enableHighAccuracy: true,
                timeout: 10000,
                maximumAge: 60000
            }
        );
    }
    
    /**
     * تحميل البيانات الأولية
     */
    async loadInitialData() {
        try {
            await this.loadStores();
        } catch (error) {
            console.error('خطأ في تحميل البيانات:', error);
        }
    }
    
    /**
     * تحميل المتاجر
     */
    async loadStores() {
        try {
            const response = await fetch('/api/stores');
            if (!response.ok) {
                throw new Error('فشل في تحميل المتاجر');
            }
            
            this.stores = await response.json();
            this.renderStores();
            this.renderStoreMarkers();
            
        } catch (error) {
            console.error('خطأ في تحميل المتاجر:', error);
            this.showError('حدث خطأ في تحميل المتاجر');
        }
    }
    
    /**
     * عرض المتاجر في القائمة
     */
    renderStores() {
        const storesList = document.getElementById('mobile-stores-list');
        if (!storesList) return;
        
        if (this.stores.length === 0) {
            storesList.innerHTML = `
                <div style="text-align: center; padding: 40px 20px; color: #6b7280;">
                    <i class="fas fa-store" style="font-size: 48px; margin-bottom: 16px; color: #d1d5db;"></i>
                    <p>لا توجد متاجر مضافة بعد</p>
                    <p style="font-size: 14px;">انقر على "إضافة" لإضافة أول متجر</p>
                </div>
            `;
            return;
        }
        
        storesList.innerHTML = this.stores.map(store => `
            <div class="mobile-card" data-store-id="${store.id}">
                <div class="mobile-card-body">
                    <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 12px;">
                        <div style="flex: 1;">
                            <h3 style="margin: 0 0 4px 0; font-size: 18px; font-weight: 600; color: #111827;">
                                ${store.name || 'متجر بدون اسم'}
                            </h3>
                            ${store.phone ? `
                                <p style="margin: 0; color: #6b7280; font-size: 14px;">
                                    <i class="fas fa-phone" style="margin-left: 8px;"></i>
                                    ${store.phone}
                                </p>
                            ` : ''}
                            ${store.address ? `
                                <p style="margin: 4px 0 0 0; color: #6b7280; font-size: 14px;">
                                    <i class="fas fa-map-marker-alt" style="margin-left: 8px;"></i>
                                    ${store.address}
                                </p>
                            ` : ''}
                        </div>
                        ${store.image_path ? `
                            <img src="/${store.image_path}" alt="${store.name}" 
                                 style="width: 60px; height: 60px; border-radius: 8px; object-fit: cover; margin-right: 12px;">
                        ` : ''}
                    </div>
                    
                    <div style="display: flex; gap: 8px;">
                        <button class="mobile-button mobile-button-outline mobile-button-sm" onclick="mobileInterface.locateStore(${store.id})">
                            <i class="fas fa-map-marker-alt"></i>
                            موقع
                        </button>
                        <button class="mobile-button mobile-button-secondary mobile-button-sm" onclick="mobileInterface.shareStore(${store.id})">
                            <i class="fab fa-whatsapp"></i>
                            مشاركة
                        </button>
                    </div>
                </div>
            </div>
        `).join('');
    }
    
    /**
     * عرض علامات المتاجر على الخريطة
     */
    renderStoreMarkers() {
        // إزالة العلامات السابقة
        this.markers.forEach(marker => {
            this.map.removeLayer(marker);
        });
        this.markers = [];
        
        // إضافة علامات جديدة
        this.stores.forEach(store => {
            if (store.latitude && store.longitude) {
                const marker = L.marker([store.latitude, store.longitude], {
                    icon: L.divIcon({
                        html: '<i class="fas fa-store" style="color: #d50000; font-size: 20px;"></i>',
                        iconSize: [20, 20],
                        iconAnchor: [10, 20],
                        className: 'store-marker'
                    })
                }).addTo(this.map);
                
                // إضافة نافذة منبثقة
                marker.bindPopup(`
                    <div style="text-align: center; min-width: 200px;">
                        <h4 style="margin: 0 0 8px 0; color: #d50000;">${store.name}</h4>
                        ${store.phone ? `<p style="margin: 4px 0;"><i class="fas fa-phone"></i> ${store.phone}</p>` : ''}
                        ${store.address ? `<p style="margin: 4px 0;"><i class="fas fa-map-marker-alt"></i> ${store.address}</p>` : ''}
                        <button onclick="mobileInterface.shareStore(${store.id})" class="mobile-button mobile-button-primary mobile-button-sm" style="margin-top: 8px;">
                            <i class="fab fa-whatsapp"></i> مشاركة
                        </button>
                    </div>
                `);
                
                this.markers.push(marker);
            }
        });
    }
    
    /**
     * تحديد موقع متجر على الخريطة
     */
    locateStore(storeId) {
        const store = this.stores.find(s => s.id == storeId);
        if (!store || !store.latitude || !store.longitude) {
            this.showError('لا يوجد موقع محدد لهذا المتجر');
            return;
        }
        
        // التبديل إلى تبويب الخريطة
        this.switchTab('map');
        
        // الانتقال إلى موقع المتجر
        this.map.setView([store.latitude, store.longitude], 16);
        
        // العثور على العلامة وفتح النافذة المنبثقة
        const marker = this.markers.find(m => {
            const markerLatLng = m.getLatLng();
            return Math.abs(markerLatLng.lat - store.latitude) < 0.0001 && 
                   Math.abs(markerLatLng.lng - store.longitude) < 0.0001;
        });
        
        if (marker) {
            marker.openPopup();
        }
    }
    
    /**
     * مشاركة متجر عبر واتساب
     */
    shareStore(storeId) {
        const store = this.stores.find(s => s.id == storeId);
        if (!store) return;
        
        let message = `🏪 ${store.name}\n`;
        
        if (store.phone) {
            message += `📞 ${store.phone}\n`;
        }
        
        if (store.address) {
            message += `📍 ${store.address}\n`;
        }
        
        if (store.latitude && store.longitude) {
            const mapUrl = `https://www.google.com/maps?q=${store.latitude},${store.longitude}`;
            message += `🗺️ ${mapUrl}`;
        }
        
        const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(message)}`;
        window.open(whatsappUrl, '_blank');
    }
    
    /**
     * البحث في المتاجر
     */
    searchStores(query) {
        if (!query.trim()) {
            this.renderStores();
            return;
        }
        
        const filteredStores = this.stores.filter(store => 
            store.name?.toLowerCase().includes(query.toLowerCase()) ||
            store.phone?.includes(query) ||
            store.address?.toLowerCase().includes(query.toLowerCase())
        );
        
        const originalStores = this.stores;
        this.stores = filteredStores;
        this.renderStores();
        this.stores = originalStores;
    }
    
    /**
     * إرسال نموذج إضافة متجر
     */
    async submitStore() {
        if (this.isLoading) return;
        
        const name = document.getElementById('storeName').value.trim();
        const phone = document.getElementById('storePhone').value.trim();
        const address = document.getElementById('storeAddress').value.trim();
        const imageFile = document.getElementById('storeImage').files[0];
        
        if (!name || !address) {
            this.showError('يرجى ملء جميع الحقول المطلوبة');
            return;
        }
        
        if (!this.selectedLocation) {
            this.showError('يرجى تحديد موقع المتجر على الخريطة');
            return;
        }
        
        this.isLoading = true;
        
        try {
            const formData = new FormData();
            formData.append('name', name);
            formData.append('phone', phone);
            formData.append('full_address', address);
            formData.append('latitude', this.selectedLocation.lat);
            formData.append('longitude', this.selectedLocation.lng);
            
            if (imageFile) {
                formData.append('image', imageFile);
            }
            
            const response = await fetch('/api/stores', {
                method: 'POST',
                body: formData
            });
            
            if (!response.ok) {
                throw new Error('فشل في إضافة المتجر');
            }
            
            const result = await response.json();
            
            if (result.success) {
                this.showSuccess('تم إضافة المتجر بنجاح');
                this.resetForm();
                await this.loadStores();
                this.switchTab('stores');
            } else {
                throw new Error(result.error || 'فشل في إضافة المتجر');
            }
            
        } catch (error) {
            console.error('خطأ في إضافة المتجر:', error);
            this.showError(error.message || 'حدث خطأ في إضافة المتجر');
        } finally {
            this.isLoading = false;
        }
    }
    
    /**
     * إعادة تعيين النموذج
     */
    resetForm() {
        document.getElementById('mobile-store-form').reset();
        
        if (this.selectedMarker) {
            this.map.removeLayer(this.selectedMarker);
            this.selectedMarker = null;
        }
        
        this.selectedLocation = null;
        
        const locationText = document.getElementById('selectedLocationText');
        if (locationText) {
            locationText.innerHTML = 'انقر على الخريطة لتحديد الموقع';
            locationText.style.background = '#f3f4f6';
            locationText.style.color = '#6b7280';
        }
    }
    
    /**
     * عرض رسالة نجاح
     */
    showSuccess(message) {
        this.showAlert(message, 'success');
    }
    
    /**
     * عرض رسالة خطأ
     */
    showError(message) {
        this.showAlert(message, 'error');
    }
    
    /**
     * عرض رسالة
     */
    showAlert(message, type = 'info') {
        const alertsContainer = document.getElementById('mobile-alerts');
        if (!alertsContainer) return;
        
        const alert = document.createElement('div');
        alert.className = `mobile-alert mobile-alert-${type}`;
        alert.innerHTML = `
            <div style="display: flex; align-items: center; justify-content: space-between;">
                <span>${message}</span>
                <button onclick="this.parentElement.parentElement.remove()" style="background: none; border: none; font-size: 18px; cursor: pointer;">×</button>
            </div>
        `;
        
        alertsContainer.appendChild(alert);
        
        // إزالة الرسالة تلقائياً بعد 5 ثواني
        setTimeout(() => {
            if (alert.parentElement) {
                alert.remove();
            }
        }, 5000);
    }
}

// تهيئة واجهة الهاتف المحمول عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    window.mobileInterface = new MobileInterface();
});

// إضافة أنماط CSS للرسوم المتحركة
const style = document.createElement('style');
style.textContent = `
    @keyframes pulse {
        0%, 100% { transform: scale(1); opacity: 1; }
        50% { transform: scale(1.1); opacity: 0.7; }
    }
    
    .mobile-tab-content {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }
    
    .mobile-card {
        transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    }
    
    .mobile-card:hover {
        transform: translateY(-2px);
    }
`;
document.head.appendChild(style);
