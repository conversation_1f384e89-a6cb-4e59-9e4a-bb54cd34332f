<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>اختبار واجهة الهاتف المحمول - Loacker</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&family=Cairo:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- Mobile CSS Files -->
    <link rel="stylesheet" href="static/css/mobile-enhanced.css">
    <link rel="stylesheet" href="static/css/mobile-fixes.css">
    
    <style>
        /* إجبار عرض واجهة الهاتف للاختبار */
        body {
            font-family: 'Tajawal', 'Cairo', sans-serif !important;
        }
        
        body.mobile-device .mobile-view {
            display: block !important;
        }
        
        body.mobile-device .container-fluid {
            display: none !important;
        }
        
        /* محاكاة جهاز محمول */
        .mobile-simulator {
            max-width: 375px;
            margin: 0 auto;
            border: 2px solid #333;
            border-radius: 20px;
            overflow: hidden;
            background: #000;
            padding: 10px;
        }
        
        .mobile-screen {
            background: #fff;
            border-radius: 15px;
            overflow: hidden;
            height: 667px;
            position: relative;
        }
    </style>
</head>
<body class="mobile-device">
    <div class="mobile-simulator">
        <div class="mobile-screen">
            <!-- واجهة الهاتف المحمول -->
            <div class="mobile-view">
                <!-- الرأس -->
                <header class="mobile-header">
                    <div class="mobile-logo">
                        <div class="mobile-logo-icon">
                            <i class="fas fa-store"></i>
                        </div>
                        <div class="mobile-logo-text">Loacker</div>
                    </div>
                    <button class="mobile-menu-button" id="mobileMenuButton">
                        <i class="fas fa-bars"></i>
                    </button>
                </header>
                
                <!-- المحتوى الرئيسي -->
                <main class="mobile-content">
                    <!-- محتوى الخريطة -->
                    <div class="mobile-tab-content active" id="mobile-map-content">
                        <div class="mobile-card">
                            <div class="mobile-card-body" style="padding: 0;">
                                <div id="mobile-map" style="height: 300px; border-radius: 16px; overflow: hidden; background: #e5e7eb; display: flex; align-items: center; justify-content: center;">
                                    <div style="text-align: center; color: #6b7280;">
                                        <i class="fas fa-map" style="font-size: 48px; margin-bottom: 16px;"></i>
                                        <p>الخريطة ستظهر هنا</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- أزرار الخريطة -->
                        <div style="display: flex; gap: 12px; margin-top: 16px;">
                            <button class="mobile-button mobile-button-outline" id="getCurrentLocationBtn">
                                <i class="fas fa-location-arrow"></i>
                                موقعي
                            </button>
                            <button class="mobile-button mobile-button-secondary" id="searchLocationBtn">
                                <i class="fas fa-search"></i>
                                بحث
                            </button>
                        </div>
                    </div>
                    
                    <!-- محتوى قائمة المتاجر -->
                    <div class="mobile-tab-content" id="mobile-stores-content">
                        <!-- شريط البحث -->
                        <div class="mobile-form-group">
                            <input type="text" class="mobile-input" id="storeSearchInput" placeholder="البحث عن متجر...">
                        </div>
                        
                        <!-- قائمة المتاجر -->
                        <div id="mobile-stores-list">
                            <!-- متجر تجريبي -->
                            <div class="mobile-card">
                                <div class="mobile-card-body">
                                    <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 12px;">
                                        <div style="flex: 1;">
                                            <h3 style="margin: 0 0 4px 0; font-size: 18px; font-weight: 600; color: #111827;">
                                                متجر Loacker الرئيسي
                                            </h3>
                                            <p style="margin: 0; color: #6b7280; font-size: 14px;">
                                                <i class="fas fa-phone" style="margin-left: 8px;"></i>
                                                +218 91 234 5678
                                            </p>
                                            <p style="margin: 4px 0 0 0; color: #6b7280; font-size: 14px;">
                                                <i class="fas fa-map-marker-alt" style="margin-left: 8px;"></i>
                                                شارع الجمهورية، طرابلس
                                            </p>
                                        </div>
                                    </div>
                                    
                                    <div style="display: flex; gap: 8px;">
                                        <button class="mobile-button mobile-button-outline mobile-button-sm">
                                            <i class="fas fa-map-marker-alt"></i>
                                            موقع
                                        </button>
                                        <button class="mobile-button mobile-button-secondary mobile-button-sm">
                                            <i class="fab fa-whatsapp"></i>
                                            مشاركة
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- متجر تجريبي آخر -->
                            <div class="mobile-card">
                                <div class="mobile-card-body">
                                    <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 12px;">
                                        <div style="flex: 1;">
                                            <h3 style="margin: 0 0 4px 0; font-size: 18px; font-weight: 600; color: #111827;">
                                                متجر Loacker الفرع الثاني
                                            </h3>
                                            <p style="margin: 0; color: #6b7280; font-size: 14px;">
                                                <i class="fas fa-phone" style="margin-left: 8px;"></i>
                                                +218 92 345 6789
                                            </p>
                                            <p style="margin: 4px 0 0 0; color: #6b7280; font-size: 14px;">
                                                <i class="fas fa-map-marker-alt" style="margin-left: 8px;"></i>
                                                شارع عمر المختار، بنغازي
                                            </p>
                                        </div>
                                    </div>
                                    
                                    <div style="display: flex; gap: 8px;">
                                        <button class="mobile-button mobile-button-outline mobile-button-sm">
                                            <i class="fas fa-map-marker-alt"></i>
                                            موقع
                                        </button>
                                        <button class="mobile-button mobile-button-secondary mobile-button-sm">
                                            <i class="fab fa-whatsapp"></i>
                                            مشاركة
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- محتوى إضافة متجر -->
                    <div class="mobile-tab-content" id="mobile-add-content">
                        <form id="mobile-store-form">
                            <div class="mobile-form-group">
                                <label class="mobile-label">اسم المتجر *</label>
                                <input type="text" class="mobile-input" id="storeName" placeholder="أدخل اسم المتجر" required>
                            </div>
                            
                            <div class="mobile-form-group">
                                <label class="mobile-label">رقم الهاتف</label>
                                <input type="tel" class="mobile-input" id="storePhone" placeholder="أدخل رقم الهاتف">
                            </div>
                            
                            <div class="mobile-form-group">
                                <label class="mobile-label">العنوان *</label>
                                <input type="text" class="mobile-input" id="storeAddress" placeholder="أدخل عنوان المتجر" required>
                            </div>
                            
                            <div class="mobile-form-group">
                                <label class="mobile-label">صورة المتجر</label>
                                <input type="file" class="mobile-input" id="storeImage" accept="image/*">
                            </div>
                            
                            <div class="mobile-form-group">
                                <label class="mobile-label">الموقع على الخريطة</label>
                                <div id="selectedLocationText" style="padding: 12px; background: #f3f4f6; border-radius: 8px; font-size: 14px; color: #6b7280;">
                                    انقر على الخريطة لتحديد الموقع
                                </div>
                            </div>
                            
                            <button type="submit" class="mobile-button mobile-button-primary" style="width: 100%;">
                                <i class="fas fa-plus"></i>
                                إضافة المتجر
                            </button>
                        </form>
                    </div>
                </main>
                
                <!-- التبويبات السفلية -->
                <nav class="mobile-tabs">
                    <button class="mobile-tab-button active" data-tab="map">
                        <i class="fas fa-map mobile-tab-icon"></i>
                        <span>الخريطة</span>
                    </button>
                    <button class="mobile-tab-button" data-tab="stores">
                        <i class="fas fa-list mobile-tab-icon"></i>
                        <span>المتاجر</span>
                    </button>
                    <button class="mobile-tab-button" data-tab="add">
                        <i class="fas fa-plus mobile-tab-icon"></i>
                        <span>إضافة</span>
                    </button>
                </nav>
                
                <!-- منطقة الرسائل -->
                <div id="mobile-alerts" style="position: fixed; top: 80px; left: 20px; right: 20px; z-index: 1001;">
                    <!-- رسالة تجريبية -->
                    <div class="mobile-alert mobile-alert-success" style="display: none;" id="testAlert">
                        <div style="display: flex; align-items: center; justify-content: space-between;">
                            <span>مرحباً بك في واجهة الهاتف المحمول المحسنة!</span>
                            <button onclick="this.parentElement.parentElement.style.display='none'" style="background: none; border: none; font-size: 18px; cursor: pointer;">×</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- JavaScript -->
    <script>
        // محاكاة وظائف التبويبات
        document.querySelectorAll('.mobile-tab-button').forEach(button => {
            button.addEventListener('click', (e) => {
                const tab = e.currentTarget.dataset.tab;
                
                // إزالة الفئة النشطة من جميع الأزرار والمحتويات
                document.querySelectorAll('.mobile-tab-button').forEach(btn => {
                    btn.classList.remove('active');
                });
                document.querySelectorAll('.mobile-tab-content').forEach(content => {
                    content.classList.remove('active');
                });
                
                // إضافة الفئة النشطة للتبويب المحدد
                const activeButton = document.querySelector(`[data-tab="${tab}"]`);
                const activeContent = document.getElementById(`mobile-${tab}-content`);
                
                if (activeButton) activeButton.classList.add('active');
                if (activeContent) activeContent.classList.add('active');
            });
        });
        
        // إظهار رسالة ترحيب
        setTimeout(() => {
            document.getElementById('testAlert').style.display = 'block';
        }, 1000);
        
        // محاكاة إرسال النموذج
        document.getElementById('mobile-store-form').addEventListener('submit', (e) => {
            e.preventDefault();
            
            const alertsContainer = document.getElementById('mobile-alerts');
            const alert = document.createElement('div');
            alert.className = 'mobile-alert mobile-alert-success';
            alert.innerHTML = `
                <div style="display: flex; align-items: center; justify-content: space-between;">
                    <span>تم إضافة المتجر بنجاح! (تجريبي)</span>
                    <button onclick="this.parentElement.parentElement.remove()" style="background: none; border: none; font-size: 18px; cursor: pointer;">×</button>
                </div>
            `;
            
            alertsContainer.appendChild(alert);
            
            // إزالة الرسالة تلقائياً بعد 3 ثواني
            setTimeout(() => {
                if (alert.parentElement) {
                    alert.remove();
                }
            }, 3000);
            
            // إعادة تعيين النموذج
            document.getElementById('mobile-store-form').reset();
        });
        
        // محاكاة البحث
        document.getElementById('storeSearchInput').addEventListener('input', (e) => {
            const query = e.target.value.toLowerCase();
            const stores = document.querySelectorAll('#mobile-stores-list .mobile-card');
            
            stores.forEach(store => {
                const text = store.textContent.toLowerCase();
                if (text.includes(query)) {
                    store.style.display = 'block';
                } else {
                    store.style.display = 'none';
                }
            });
        });
        
        console.log('✅ تم تحميل صفحة اختبار واجهة الهاتف المحمول بنجاح');
    </script>
</body>
</html>
