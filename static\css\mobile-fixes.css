/**
 * إصلاحات واجهة الهاتف المحمول - <PERSON><PERSON>er
 * ملف لإصلاح المشاكل الشائعة في واجهة الهاتف المحمول
 */

/* ===== إصلاح مشكلة الشاشة السوداء ===== */
body.mobile-device {
    background-color: #f8f9fa !important;
    color: #212529 !important;
}

/* التأكد من عرض واجهة الهاتف بشكل صحيح */
body.mobile-device .mobile-view {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    background-color: #f8f9fa !important;
    min-height: 100vh !important;
}

/* إخفاء واجهة سطح المكتب تماماً على الهواتف */
body.mobile-device .container-fluid,
body.mobile-device .d-none.d-md-block,
body.mobile-device .d-md-none {
    display: none !important;
}

/* إظهار عناصر الهاتف المحمول فقط */
body.mobile-device .d-md-none {
    display: block !important;
}

/* ===== إصلاح مشاكل التداخل ===== */
.mobile-view * {
    box-sizing: border-box;
}

/* منع التداخل في العناصر */
.mobile-view .card,
.mobile-view .mobile-card {
    position: relative;
    z-index: 1;
}

/* إصلاح مشكلة الخريطة */
.mobile-view #mobile-map {
    position: relative !important;
    z-index: 1 !important;
    background-color: #e5e7eb !important;
}

/* التأكد من عمل الخريطة */
.mobile-view .leaflet-container {
    background-color: #e5e7eb !important;
    font-family: inherit !important;
}

/* ===== إصلاح مشاكل التبويبات ===== */
.mobile-tabs {
    background-color: #ffffff !important;
    border-top: 1px solid #e5e7eb !important;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1) !important;
}

.mobile-tab-button {
    background-color: transparent !important;
    border: none !important;
    color: #6b7280 !important;
}

.mobile-tab-button.active {
    color: #d50000 !important;
}

/* ===== إصلاح مشاكل النصوص ===== */
.mobile-view {
    font-family: 'Tajawal', 'Cairo', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif !important;
    direction: rtl !important;
    text-align: right !important;
}

/* إصلاح مشاكل الأزرار */
.mobile-view button,
.mobile-view .btn,
.mobile-button {
    font-family: inherit !important;
    direction: rtl !important;
    text-align: center !important;
}

/* ===== إصلاح مشاكل النماذج ===== */
.mobile-view input,
.mobile-view select,
.mobile-view textarea {
    font-family: inherit !important;
    direction: rtl !important;
    text-align: right !important;
}

.mobile-view input::placeholder {
    text-align: right !important;
    direction: rtl !important;
}

/* ===== إصلاح مشاكل التمرير ===== */
.mobile-view {
    overflow-x: hidden !important;
    -webkit-overflow-scrolling: touch !important;
}

/* منع التمرير الأفقي */
body.mobile-device {
    overflow-x: hidden !important;
}

/* ===== إصلاح مشاكل الأيقونات ===== */
.mobile-view i {
    font-style: normal !important;
    font-variant: normal !important;
    text-rendering: auto !important;
    -webkit-font-smoothing: antialiased !important;
}

/* ===== إصلاح مشاكل الصور ===== */
.mobile-view img {
    max-width: 100% !important;
    height: auto !important;
    border-radius: 8px !important;
}

/* ===== إصلاح مشاكل الظلال ===== */
.mobile-view .card,
.mobile-view .mobile-card {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

/* ===== إصلاح مشاكل الألوان ===== */
.mobile-view .text-primary {
    color: #d50000 !important;
}

.mobile-view .bg-primary {
    background-color: #d50000 !important;
}

.mobile-view .btn-primary {
    background-color: #d50000 !important;
    border-color: #d50000 !important;
}

/* ===== إصلاح مشاكل التجاوب ===== */
@media (max-width: 767px) {
    /* إجبار عرض واجهة الهاتف */
    .mobile-view {
        display: block !important;
    }
    
    /* إخفاء واجهة سطح المكتب */
    .container-fluid {
        display: none !important;
    }
}

/* ===== إصلاح مشاكل الهواتف الصغيرة ===== */
@media (max-width: 375px) {
    .mobile-view {
        font-size: 14px !important;
    }
    
    .mobile-header {
        height: 56px !important;
        padding: 0 16px !important;
    }
    
    .mobile-content {
        padding: 16px !important;
    }
    
    .mobile-tabs {
        height: 64px !important;
    }
}

/* ===== إصلاح مشاكل الهواتف الكبيرة ===== */
@media (min-width: 414px) and (max-width: 767px) {
    .mobile-view {
        font-size: 16px !important;
    }
    
    .mobile-header {
        height: 64px !important;
        padding: 0 20px !important;
    }
    
    .mobile-content {
        padding: 20px !important;
    }
    
    .mobile-tabs {
        height: 72px !important;
    }
}

/* ===== إصلاح مشاكل الاتجاه الأفقي ===== */
@media (orientation: landscape) and (max-height: 500px) {
    .mobile-header {
        height: 48px !important;
    }
    
    .mobile-tabs {
        height: 56px !important;
    }
    
    .mobile-view #mobile-map {
        height: 40vh !important;
    }
}

/* ===== إصلاح مشاكل Safari على iOS ===== */
@supports (-webkit-touch-callout: none) {
    .mobile-view {
        -webkit-overflow-scrolling: touch !important;
    }

    .mobile-view input,
    .mobile-view select,
    .mobile-view textarea {
        -webkit-appearance: none !important;
        appearance: none !important;
        border-radius: 8px !important;
    }

    .mobile-view button {
        -webkit-appearance: none !important;
        appearance: none !important;
    }
}

/* ===== إصلاح مشاكل الأداء ===== */
.mobile-view * {
    -webkit-transform: translateZ(0) !important;
    transform: translateZ(0) !important;
    -webkit-backface-visibility: hidden !important;
    backface-visibility: hidden !important;
}

/* تحسين الرسوم المتحركة */
.mobile-view .transition,
.mobile-view [class*="transition"] {
    -webkit-transform: translateZ(0) !important;
    transform: translateZ(0) !important;
    will-change: transform, opacity !important;
}

/* ===== إصلاح مشاكل اللمس ===== */
.mobile-view button,
.mobile-view .btn,
.mobile-view a,
.mobile-view [role="button"] {
    -webkit-tap-highlight-color: transparent !important;
    touch-action: manipulation !important;
    user-select: none !important;
    -webkit-user-select: none !important;
}

/* ===== إصلاح مشاكل التكبير ===== */
.mobile-view input,
.mobile-view select,
.mobile-view textarea {
    font-size: 16px !important; /* منع التكبير التلقائي في iOS */
}

/* ===== إصلاح مشاكل الخطوط ===== */
@font-face {
    font-family: 'Tajawal-Fallback';
    src: local('Tajawal'), local('Cairo'), local('Arial');
    font-display: swap;
}

.mobile-view {
    font-family: 'Tajawal', 'Tajawal-Fallback', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif !important;
}

/* ===== إصلاح مشاكل الوضع المظلم ===== */
@media (prefers-color-scheme: dark) {
    body.mobile-device {
        background-color: #1f2937 !important;
        color: #f9fafb !important;
    }
    
    .mobile-view {
        background-color: #1f2937 !important;
        color: #f9fafb !important;
    }
    
    .mobile-view .card,
    .mobile-view .mobile-card {
        background-color: #374151 !important;
        border-color: #4b5563 !important;
    }
    
    .mobile-view input,
    .mobile-view select,
    .mobile-view textarea {
        background-color: #374151 !important;
        border-color: #4b5563 !important;
        color: #f9fafb !important;
    }
}

/* ===== إصلاح مشاكل التحميل ===== */
.mobile-view .loading {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    min-height: 200px !important;
}

.mobile-view .spinner {
    width: 40px !important;
    height: 40px !important;
    border: 4px solid #e5e7eb !important;
    border-top: 4px solid #d50000 !important;
    border-radius: 50% !important;
    animation: spin 1s linear infinite !important;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== إصلاح مشاكل الإشعارات ===== */
.mobile-view .alert,
.mobile-view .mobile-alert {
    position: relative !important;
    z-index: 1000 !important;
    margin-bottom: 16px !important;
}

/* ===== إصلاح نهائي للتأكد من العرض ===== */
body.mobile-device .mobile-view {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
    z-index: 1 !important;
}

/* إخفاء أي عناصر قد تتداخل */
body.mobile-device > *:not(.mobile-view) {
    display: none !important;
}

body.mobile-device .mobile-view ~ * {
    display: none !important;
}

/* ===== إصلاحات إضافية للتوافق الكامل ===== */

/* إصلاح مشاكل الخطوط العربية */
.mobile-view * {
    font-family: 'Tajawal', 'Cairo', 'Segoe UI', 'Tahoma', sans-serif !important;
    text-rendering: optimizeLegibility !important;
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important;
}

/* إصلاح مشاكل الاتجاه RTL */
.mobile-view {
    direction: rtl !important;
    text-align: right !important;
}

.mobile-view input,
.mobile-view textarea,
.mobile-view select {
    direction: rtl !important;
    text-align: right !important;
}

.mobile-view input::placeholder,
.mobile-view textarea::placeholder {
    direction: rtl !important;
    text-align: right !important;
}

/* إصلاح مشاكل الأيقونات */
.mobile-view .fa,
.mobile-view .fas,
.mobile-view .far,
.mobile-view .fab {
    direction: ltr !important;
    display: inline-block !important;
}

/* إصلاح مشاكل التمرير */
.mobile-view {
    overflow-x: hidden !important;
    overflow-y: auto !important;
    -webkit-overflow-scrolling: touch !important;
    scroll-behavior: smooth !important;
}

/* منع التمرير المطاطي في iOS */
.mobile-view,
.mobile-view .mobile-content {
    overscroll-behavior: contain !important;
    -webkit-overscroll-behavior: contain !important;
}

/* إصلاح مشاكل اللمس والتفاعل */
.mobile-view button,
.mobile-view .btn,
.mobile-view a,
.mobile-view [role="button"],
.mobile-view .mobile-tab-button {
    -webkit-tap-highlight-color: rgba(213, 0, 0, 0.2) !important;
    touch-action: manipulation !important;
    user-select: none !important;
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
}

/* إصلاح مشاكل التكبير التلقائي في iOS */
.mobile-view input[type="text"],
.mobile-view input[type="email"],
.mobile-view input[type="tel"],
.mobile-view input[type="password"],
.mobile-view input[type="search"],
.mobile-view textarea,
.mobile-view select {
    font-size: 16px !important; /* منع التكبير التلقائي */
    -webkit-appearance: none !important;
    appearance: none !important;
}

/* إصلاح مشاكل الخريطة */
.mobile-view #mobile-map,
.mobile-view .leaflet-container {
    position: relative !important;
    z-index: 1 !important;
    background-color: #e5e7eb !important;
    border-radius: 16px !important;
    overflow: hidden !important;
}

.mobile-view .leaflet-control-container {
    font-family: inherit !important;
}

.mobile-view .leaflet-popup-content {
    direction: rtl !important;
    text-align: right !important;
    font-family: inherit !important;
}

/* إصلاح مشاكل النماذج */
.mobile-view form {
    width: 100% !important;
}

.mobile-view .form-group,
.mobile-view .mobile-form-group {
    margin-bottom: 20px !important;
}

.mobile-view label,
.mobile-view .mobile-label {
    display: block !important;
    margin-bottom: 8px !important;
    font-weight: 500 !important;
    color: #374151 !important;
}

/* إصلاح مشاكل الأزرار */
.mobile-view button,
.mobile-view .btn,
.mobile-view .mobile-button {
    min-height: 44px !important; /* حد أدنى للمس */
    padding: 12px 20px !important;
    border-radius: 12px !important;
    font-weight: 500 !important;
    transition: all 0.2s ease !important;
    cursor: pointer !important;
}

.mobile-view .mobile-button-primary {
    background-color: #d50000 !important;
    color: white !important;
    border: none !important;
}

.mobile-view .mobile-button-primary:hover,
.mobile-view .mobile-button-primary:focus {
    background-color: #9b0000 !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(213, 0, 0, 0.3) !important;
}

.mobile-view .mobile-button-primary:active {
    transform: translateY(0) !important;
    box-shadow: 0 2px 6px rgba(213, 0, 0, 0.3) !important;
}

/* إصلاح مشاكل البطاقات */
.mobile-view .card,
.mobile-view .mobile-card {
    background-color: white !important;
    border: 1px solid #e5e7eb !important;
    border-radius: 16px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    margin-bottom: 16px !important;
    overflow: hidden !important;
}

.mobile-view .card-body,
.mobile-view .mobile-card-body {
    padding: 20px !important;
}

/* إصلاح مشاكل التبويبات */
.mobile-view .mobile-tabs {
    position: fixed !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    background-color: white !important;
    border-top: 1px solid #e5e7eb !important;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1) !important;
    z-index: 1000 !important;
    display: flex !important;
    height: 80px !important;
    padding-bottom: env(safe-area-inset-bottom, 0px) !important;
}

.mobile-view .mobile-tab-button {
    flex: 1 !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 4px !important;
    background: none !important;
    border: none !important;
    color: #6b7280 !important;
    font-size: 12px !important;
    font-weight: 500 !important;
    transition: all 0.2s ease !important;
}

.mobile-view .mobile-tab-button.active {
    color: #d50000 !important;
}

.mobile-view .mobile-tab-button.active::after {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    width: 32px !important;
    height: 3px !important;
    background-color: #d50000 !important;
    border-radius: 0 0 2px 2px !important;
}

.mobile-view .mobile-tab-icon {
    font-size: 20px !important;
}

/* إصلاح مشاكل المحتوى */
.mobile-view .mobile-content {
    padding: 20px !important;
    margin-top: calc(64px + env(safe-area-inset-top, 0px)) !important;
    margin-bottom: calc(80px + env(safe-area-inset-bottom, 0px)) !important;
    min-height: calc(100vh - 144px - env(safe-area-inset-top, 0px) - env(safe-area-inset-bottom, 0px)) !important;
}

.mobile-view .mobile-tab-content {
    display: none !important;
    opacity: 0 !important;
    transform: translateY(20px) !important;
    transition: all 0.3s ease !important;
}

.mobile-view .mobile-tab-content.active {
    display: block !important;
    opacity: 1 !important;
    transform: translateY(0) !important;
}

/* إصلاح مشاكل الرأس */
.mobile-view .mobile-header {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    height: calc(64px + env(safe-area-inset-top, 0px)) !important;
    background-color: white !important;
    border-bottom: 1px solid #e5e7eb !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    z-index: 1000 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    padding: env(safe-area-inset-top, 0px) 20px 0 20px !important;
}

/* إصلاح مشاكل الشعار */
.mobile-view .mobile-logo {
    display: flex !important;
    align-items: center !important;
    gap: 12px !important;
}

.mobile-view .mobile-logo-icon {
    width: 40px !important;
    height: 40px !important;
    background-color: #d50000 !important;
    border-radius: 12px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    color: white !important;
    font-size: 20px !important;
}

.mobile-view .mobile-logo-text {
    font-size: 20px !important;
    font-weight: 700 !important;
    color: #d50000 !important;
}

/* إصلاح مشاكل الإشعارات */
.mobile-view .mobile-alert {
    padding: 16px !important;
    border-radius: 12px !important;
    margin-bottom: 16px !important;
    font-size: 14px !important;
    font-weight: 500 !important;
}

.mobile-view .mobile-alert-success {
    background-color: #d1fae5 !important;
    color: #065f46 !important;
    border: 1px solid #a7f3d0 !important;
}

.mobile-view .mobile-alert-error {
    background-color: #fee2e2 !important;
    color: #991b1b !important;
    border: 1px solid #fecaca !important;
}

/* إصلاح مشاكل التحميل */
.mobile-view .loading,
.mobile-view .mobile-loading {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    min-height: 200px !important;
}

.mobile-view .spinner {
    width: 40px !important;
    height: 40px !important;
    border: 4px solid #e5e7eb !important;
    border-top: 4px solid #d50000 !important;
    border-radius: 50% !important;
    animation: spin 1s linear infinite !important;
}

/* إصلاح مشاكل الصور */
.mobile-view img {
    max-width: 100% !important;
    height: auto !important;
    border-radius: 8px !important;
    object-fit: cover !important;
}

/* إصلاح مشاكل البحث */
.mobile-view .search-input,
.mobile-view input[type="search"] {
    width: 100% !important;
    padding: 16px !important;
    border: 2px solid #e5e7eb !important;
    border-radius: 12px !important;
    font-size: 16px !important;
    background-color: white !important;
}

.mobile-view .search-input:focus,
.mobile-view input[type="search"]:focus {
    outline: none !important;
    border-color: #d50000 !important;
    box-shadow: 0 0 0 3px rgba(213, 0, 0, 0.1) !important;
}
