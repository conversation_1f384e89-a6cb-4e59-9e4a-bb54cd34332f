/**
 * واجهة الهاتف المحمول المحسنة - Loacker
 * إصدار 2025 - متوافق مع جميع الأجهزة المحمولة
 * تم تصميمه ليكون سريع وسلس ومتجاوب
 */

/* ===== المتغيرات الأساسية ===== */
:root {
    /* ألوان Loacker */
    --loacker-primary: #d50000;
    --loacker-primary-light: #ff5131;
    --loacker-primary-dark: #9b0000;
    --loacker-primary-alpha: rgba(213, 0, 0, 0.1);
    
    /* ألوان النظام */
    --color-white: #ffffff;
    --color-black: #000000;
    --color-gray-50: #f9fafb;
    --color-gray-100: #f3f4f6;
    --color-gray-200: #e5e7eb;
    --color-gray-300: #d1d5db;
    --color-gray-400: #9ca3af;
    --color-gray-500: #6b7280;
    --color-gray-600: #4b5563;
    --color-gray-700: #374151;
    --color-gray-800: #1f2937;
    --color-gray-900: #111827;
    
    /* أبعاد الواجهة */
    --header-height: 64px;
    --footer-height: 80px;
    --content-padding: 20px;
    --border-radius: 16px;
    --border-radius-sm: 12px;
    --border-radius-lg: 20px;
    
    /* المناطق الآمنة */
    --safe-area-top: env(safe-area-inset-top, 0px);
    --safe-area-bottom: env(safe-area-inset-bottom, 0px);
    --safe-area-left: env(safe-area-inset-left, 0px);
    --safe-area-right: env(safe-area-inset-right, 0px);
    
    /* الظلال */
    --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* الانتقالات */
    --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
    
    /* الخطوط */
    --font-family-primary: 'Tajawal', 'Cairo', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
}

/* ===== إعادة تعيين أساسية ===== */
* {
    box-sizing: border-box;
    -webkit-tap-highlight-color: transparent;
}

/* ===== الهيكل الأساسي ===== */
body.mobile-device {
    margin: 0;
    padding: 0;
    font-family: var(--font-family-primary);
    font-size: var(--font-size-base);
    line-height: 1.5;
    color: var(--color-gray-900);
    background-color: var(--color-gray-50);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    overflow-x: hidden;
}

/* واجهة الهاتف المحمول */
.mobile-view {
    display: none;
    position: relative;
    width: 100%;
    min-height: 100vh;
    background-color: var(--color-gray-50);
    overflow-x: hidden;
    -webkit-overflow-scrolling: touch;
}

/* عرض واجهة الهاتف على الأجهزة المحمولة */
body.mobile-device .mobile-view {
    display: flex;
    flex-direction: column;
}

/* إخفاء واجهة سطح المكتب */
body.mobile-device .container-fluid {
    display: none !important;
}

/* ===== الرأس ===== */
.mobile-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: calc(var(--header-height) + var(--safe-area-top));
    background-color: var(--color-white);
    border-bottom: 1px solid var(--color-gray-200);
    box-shadow: var(--shadow-sm);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--safe-area-top) var(--content-padding) 0;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

/* شعار Loacker */
.mobile-logo {
    display: flex;
    align-items: center;
    gap: 12px;
}

.mobile-logo-icon {
    width: 40px;
    height: 40px;
    background-color: var(--loacker-primary);
    border-radius: var(--border-radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-white);
    font-size: 1.25rem;
    box-shadow: var(--shadow-sm);
}

.mobile-logo-text {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--loacker-primary);
    letter-spacing: -0.025em;
}

/* زر القائمة */
.mobile-menu-button {
    width: 44px;
    height: 44px;
    border: none;
    border-radius: var(--border-radius-sm);
    background-color: var(--color-gray-100);
    color: var(--color-gray-600);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    transition: all var(--transition-fast);
    cursor: pointer;
}

.mobile-menu-button:hover {
    background-color: var(--color-gray-200);
    color: var(--color-gray-700);
}

.mobile-menu-button:active {
    transform: scale(0.95);
    background-color: var(--color-gray-300);
}

/* ===== المحتوى الرئيسي ===== */
.mobile-content {
    flex: 1;
    margin-top: calc(var(--header-height) + var(--safe-area-top));
    margin-bottom: calc(var(--footer-height) + var(--safe-area-bottom));
    padding: var(--content-padding);
    min-height: calc(100vh - var(--header-height) - var(--footer-height) - var(--safe-area-top) - var(--safe-area-bottom));
}

/* ===== التبويبات ===== */
.mobile-tabs {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: calc(var(--footer-height) + var(--safe-area-bottom));
    background-color: var(--color-white);
    border-top: 1px solid var(--color-gray-200);
    box-shadow: 0 -4px 6px -1px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    display: flex;
    padding-bottom: var(--safe-area-bottom);
}

.mobile-tab-button {
    flex: 1;
    border: none;
    background: none;
    padding: 12px 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 4px;
    color: var(--color-gray-500);
    font-size: var(--font-size-xs);
    font-weight: 500;
    transition: all var(--transition-fast);
    cursor: pointer;
    position: relative;
}

.mobile-tab-button.active {
    color: var(--loacker-primary);
}

.mobile-tab-button.active::after {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 32px;
    height: 3px;
    background-color: var(--loacker-primary);
    border-radius: 0 0 2px 2px;
}

.mobile-tab-icon {
    font-size: 1.5rem;
    transition: transform var(--transition-fast);
}

.mobile-tab-button.active .mobile-tab-icon {
    transform: scale(1.1);
}

/* ===== محتوى التبويبات ===== */
.mobile-tab-content {
    display: none;
    opacity: 0;
    transform: translateY(20px);
    transition: all var(--transition-normal);
}

.mobile-tab-content.active {
    display: block;
    opacity: 1;
    transform: translateY(0);
}

/* ===== البطاقات ===== */
.mobile-card {
    background-color: var(--color-white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    margin-bottom: 16px;
    overflow: hidden;
    transition: all var(--transition-fast);
}

.mobile-card:hover {
    box-shadow: var(--shadow-md);
}

.mobile-card-header {
    padding: 20px;
    border-bottom: 1px solid var(--color-gray-200);
}

.mobile-card-body {
    padding: 20px;
}

.mobile-card-footer {
    padding: 16px 20px;
    background-color: var(--color-gray-50);
    border-top: 1px solid var(--color-gray-200);
}

/* ===== الأزرار ===== */
.mobile-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 20px;
    border: none;
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-base);
    font-weight: 500;
    text-decoration: none;
    transition: all var(--transition-fast);
    cursor: pointer;
    min-height: 44px;
    position: relative;
    overflow: hidden;
}

.mobile-button-primary {
    background-color: var(--loacker-primary);
    color: var(--color-white);
    box-shadow: var(--shadow-sm);
}

.mobile-button-primary:hover {
    background-color: var(--loacker-primary-dark);
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

.mobile-button-primary:active {
    transform: translateY(0);
    box-shadow: var(--shadow-sm);
}

.mobile-button-secondary {
    background-color: var(--color-gray-100);
    color: var(--color-gray-700);
}

.mobile-button-secondary:hover {
    background-color: var(--color-gray-200);
}

.mobile-button-outline {
    background-color: transparent;
    color: var(--loacker-primary);
    border: 2px solid var(--loacker-primary);
}

.mobile-button-outline:hover {
    background-color: var(--loacker-primary);
    color: var(--color-white);
}

/* أحجام الأزرار */
.mobile-button-sm {
    padding: 8px 16px;
    font-size: var(--font-size-sm);
    min-height: 36px;
}

.mobile-button-lg {
    padding: 16px 24px;
    font-size: var(--font-size-lg);
    min-height: 52px;
}

/* ===== حقول الإدخال ===== */
.mobile-input {
    width: 100%;
    padding: 16px;
    border: 2px solid var(--color-gray-200);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-base);
    font-family: inherit;
    background-color: var(--color-white);
    transition: all var(--transition-fast);
    min-height: 52px;
}

.mobile-input:focus {
    outline: none;
    border-color: var(--loacker-primary);
    box-shadow: 0 0 0 3px var(--loacker-primary-alpha);
}

.mobile-input::placeholder {
    color: var(--color-gray-400);
}

/* ===== التسميات ===== */
.mobile-label {
    display: block;
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--color-gray-700);
    margin-bottom: 8px;
}

/* ===== مجموعات النماذج ===== */
.mobile-form-group {
    margin-bottom: 20px;
}

/* ===== الرسائل ===== */
.mobile-alert {
    padding: 16px;
    border-radius: var(--border-radius-sm);
    margin-bottom: 16px;
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.mobile-alert-success {
    background-color: #d1fae5;
    color: #065f46;
    border: 1px solid #a7f3d0;
}

.mobile-alert-error {
    background-color: #fee2e2;
    color: #991b1b;
    border: 1px solid #fecaca;
}

.mobile-alert-warning {
    background-color: #fef3c7;
    color: #92400e;
    border: 1px solid #fde68a;
}

.mobile-alert-info {
    background-color: #dbeafe;
    color: #1e40af;
    border: 1px solid #93c5fd;
}
