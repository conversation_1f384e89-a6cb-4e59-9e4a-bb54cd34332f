/**
 * تهيئة واجهة الهاتف المحمول - Loacker
 * ملف التهيئة الرئيسي لضمان عمل واجهة الهاتف بشكل صحيح
 */

(function() {
    'use strict';
    
    // متغيرات عامة
    let isInitialized = false;
    let deviceInfo = null;
    
    /**
     * كشف نوع الجهاز
     */
    function detectDevice() {
        const userAgent = navigator.userAgent || navigator.vendor || window.opera;
        const screenWidth = window.innerWidth;
        const screenHeight = window.innerHeight;
        
        const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent) || 
                        screenWidth <= 768;
        
        const isTablet = /iPad|Android/i.test(userAgent) && screenWidth >= 768 && screenWidth <= 1024;
        
        const isIOS = /iPad|iPhone|iPod/.test(userAgent);
        const isAndroid = /Android/.test(userAgent);
        
        return {
            isMobile,
            isTablet,
            isDesktop: !isMobile && !isTablet,
            isIOS,
            isAndroid,
            screenWidth,
            screenHeight,
            userAgent
        };
    }
    
    /**
     * تطبيق فئات CSS للجهاز
     */
    function applyDeviceClasses() {
        const body = document.body;
        const html = document.documentElement;
        
        // إزالة جميع فئات الأجهزة السابقة
        body.classList.remove('mobile-device', 'tablet-device', 'desktop-device', 'ios-device', 'android-device');
        
        if (deviceInfo.isMobile) {
            body.classList.add('mobile-device');
            
            if (deviceInfo.isIOS) {
                body.classList.add('ios-device');
            } else if (deviceInfo.isAndroid) {
                body.classList.add('android-device');
            }
            
            // إضافة متغيرات CSS
            html.style.setProperty('--screen-width', deviceInfo.screenWidth + 'px');
            html.style.setProperty('--screen-height', deviceInfo.screenHeight + 'px');
            
            console.log('📱 تم تطبيق فئات الجهاز المحمول');
            
        } else if (deviceInfo.isTablet) {
            body.classList.add('tablet-device');
            console.log('📱 تم تطبيق فئات الجهاز اللوحي');
            
        } else {
            body.classList.add('desktop-device');
            console.log('🖥️ تم تطبيق فئات سطح المكتب');
        }
    }
    
    /**
     * إنشاء واجهة الهاتف المحمول إذا لم تكن موجودة
     */
    function createMobileInterface() {
        if (!deviceInfo.isMobile) return;
        
        let mobileView = document.querySelector('.mobile-view');
        
        if (!mobileView) {
            console.log('📱 إنشاء واجهة الهاتف المحمول...');
            
            mobileView = document.createElement('div');
            mobileView.className = 'mobile-view';
            mobileView.style.display = 'none'; // مخفي في البداية
            
            // إضافة الهيكل الأساسي
            mobileView.innerHTML = `
                <div class="mobile-loading" style="
                    position: fixed;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: #f8f9fa;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    z-index: 9999;
                ">
                    <div style="text-align: center;">
                        <div style="
                            width: 40px;
                            height: 40px;
                            border: 4px solid #e5e7eb;
                            border-top: 4px solid #d50000;
                            border-radius: 50%;
                            animation: spin 1s linear infinite;
                            margin: 0 auto 16px;
                        "></div>
                        <p style="color: #6b7280; font-size: 14px; margin: 0;">جاري تحميل واجهة الهاتف...</p>
                    </div>
                </div>
            `;
            
            document.body.appendChild(mobileView);
            
            // إضافة أنماط الرسوم المتحركة
            const style = document.createElement('style');
            style.textContent = `
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            `;
            document.head.appendChild(style);
        }
        
        return mobileView;
    }
    
    /**
     * إخفاء واجهة سطح المكتب على الهواتف
     */
    function hideMobileInterface() {
        if (!deviceInfo.isMobile) return;
        
        // إخفاء واجهة سطح المكتب
        const desktopElements = document.querySelectorAll('.container-fluid, .d-none.d-md-block');
        desktopElements.forEach(element => {
            element.style.display = 'none';
        });
        
        // إظهار واجهة الهاتف
        const mobileView = document.querySelector('.mobile-view');
        if (mobileView) {
            mobileView.style.display = 'block';
        }
        
        console.log('📱 تم إخفاء واجهة سطح المكتب وإظهار واجهة الهاتف');
    }
    
    /**
     * إعداد مستمعي الأحداث
     */
    function setupEventListeners() {
        // مستمع تغيير حجم النافذة
        let resizeTimeout;
        window.addEventListener('resize', function() {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(function() {
                deviceInfo = detectDevice();
                applyDeviceClasses();
                
                if (deviceInfo.isMobile) {
                    hideMobileInterface();
                }
            }, 250);
        });
        
        // مستمع تغيير اتجاه الشاشة
        window.addEventListener('orientationchange', function() {
            setTimeout(function() {
                deviceInfo = detectDevice();
                applyDeviceClasses();
                
                if (deviceInfo.isMobile) {
                    hideMobileInterface();
                }
            }, 300);
        });
        
        // مستمع تحميل الصفحة
        window.addEventListener('load', function() {
            if (deviceInfo.isMobile) {
                // إزالة شاشة التحميل بعد تحميل الصفحة
                setTimeout(function() {
                    const loadingElement = document.querySelector('.mobile-loading');
                    if (loadingElement) {
                        loadingElement.style.opacity = '0';
                        loadingElement.style.transition = 'opacity 0.3s ease';
                        setTimeout(function() {
                            loadingElement.remove();
                        }, 300);
                    }
                }, 1000);
            }
        });
    }
    
    /**
     * إصلاح مشاكل الخطوط
     */
    function fixFonts() {
        // التأكد من تحميل الخطوط العربية
        const fontLink = document.createElement('link');
        fontLink.rel = 'stylesheet';
        fontLink.href = 'https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&family=Cairo:wght@300;400;500;700&display=swap';
        document.head.appendChild(fontLink);
        
        // تطبيق الخط على الجسم
        document.body.style.fontFamily = "'Tajawal', 'Cairo', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif";
    }
    
    /**
     * إصلاح مشاكل الاتجاه
     */
    function fixDirection() {
        document.documentElement.dir = 'rtl';
        document.documentElement.lang = 'ar';
        document.body.style.direction = 'rtl';
        document.body.style.textAlign = 'right';
    }
    
    /**
     * إصلاح مشاكل المناطق الآمنة (Safe Areas)
     */
    function fixSafeAreas() {
        if (deviceInfo.isIOS) {
            const style = document.createElement('style');
            style.textContent = `
                :root {
                    --safe-area-inset-top: env(safe-area-inset-top, 0px);
                    --safe-area-inset-bottom: env(safe-area-inset-bottom, 0px);
                    --safe-area-inset-left: env(safe-area-inset-left, 0px);
                    --safe-area-inset-right: env(safe-area-inset-right, 0px);
                }
                
                body.mobile-device {
                    padding-top: var(--safe-area-inset-top);
                    padding-bottom: var(--safe-area-inset-bottom);
                    padding-left: var(--safe-area-inset-left);
                    padding-right: var(--safe-area-inset-right);
                }
            `;
            document.head.appendChild(style);
        }
    }
    
    /**
     * التهيئة الرئيسية
     */
    function initialize() {
        if (isInitialized) return;
        
        console.log('🚀 بدء تهيئة واجهة الهاتف المحمول...');
        
        try {
            // كشف نوع الجهاز
            deviceInfo = detectDevice();
            console.log('📱 معلومات الجهاز:', deviceInfo);
            
            // تطبيق فئات CSS
            applyDeviceClasses();
            
            // إصلاح المشاكل الأساسية
            fixFonts();
            fixDirection();
            fixSafeAreas();
            
            if (deviceInfo.isMobile) {
                // إنشاء واجهة الهاتف
                createMobileInterface();
                
                // إخفاء واجهة سطح المكتب
                hideMobileInterface();
            }
            
            // إعداد مستمعي الأحداث
            setupEventListeners();
            
            isInitialized = true;
            console.log('✅ تم تهيئة واجهة الهاتف المحمول بنجاح');
            
            // إطلاق حدث مخصص للإشارة إلى اكتمال التهيئة
            window.dispatchEvent(new CustomEvent('mobileInterfaceReady', {
                detail: { deviceInfo }
            }));
            
        } catch (error) {
            console.error('❌ خطأ في تهيئة واجهة الهاتف المحمول:', error);
        }
    }
    
    /**
     * التحقق من جاهزية DOM
     */
    function checkDOMReady() {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initialize);
        } else {
            initialize();
        }
    }
    
    // بدء التهيئة
    checkDOMReady();
    
    // تصدير الوظائف للاستخدام العام
    window.MobileInit = {
        initialize,
        detectDevice,
        getDeviceInfo: () => deviceInfo,
        isInitialized: () => isInitialized
    };
    
})();
